/**
 * Global field mappings configuration
 * This file contains reusable field mapping configurations for various data types
 * across the application.
 */

/**
 * Evidence field mappings
 */
export const evidenceFields = [
  {
    label: 'Evidence ID',
    key: '_id',
    fallback: 'N/A'
  },
  {
    label: 'Description',
    key: 'description',
    fallback: 'No description'
  },
  {
    label: 'Type',
    key: 'type',
    fallback: 'Unknown'
  },
  {
    label: 'Created At',
    key: 'createdAt',
    formatter: (value) => value ? new Date(value).toLocaleString() : 'N/A',
    fallback: 'N/A'
  },
  {
    label: 'Lab',
    key: 'lab_department',
    formatter: (labDeptArray) => {
      if (!labDeptArray || !Array.isArray(labDeptArray) || labDeptArray.length === 0) {
        return 'Not assigned';
      }
      const labNames = [...new Set(labDeptArray.map(item => item.lab?.name || 'Unknown Lab'))];
      return labNames.join(', ');
    },
    fallback: 'Not assigned'
  },
  {
    label: 'Department',
    key: 'lab_department',
    formatter: (labDeptArray) => {
      if (!labDeptArray || !Array.isArray(labDeptArray) || labDeptArray.length === 0) {
        return 'Not assigned';
      }
      const deptNames = labDeptArray.map(item => item.department?.name || 'Unknown Department');
      return deptNames.join(', ');
    },
    fallback: 'Not assigned'
  }
];

/**
 * User/Officer field mappings
 */
export const userFields = [
  {
    label: 'Name',
    key: 'name',
    fallback: 'Unknown User'
  },
  {
    label: 'Email',
    key: 'email',
    fallback: 'No email'
  },
  {
    label: 'Role',
    key: 'role',
    fallback: 'No role assigned'
  },
  {
    label: 'Badge Number',
    key: 'badgeNumber',
    fallback: 'N/A'
  },
  {
    label: 'Department',
    key: 'department',
    fallback: 'No department'
  },
  {
    label: 'Phone',
    key: 'phone',
    fallback: 'No phone number'
  }
];

/**
 * Lab field mappings
 */
export const labFields = [
  {
    label: 'Lab Name',
    key: 'name',
    fallback: 'Unknown Lab'
  },
  {
    label: 'Location',
    key: 'location',
    fallback: 'No location'
  },
  {
    label: 'Contact',
    key: 'contact',
    fallback: 'No contact info'
  },
  {
    label: 'Type',
    key: 'type',
    fallback: 'General'
  }
];

/**
 * Department field mappings
 */
export const departmentFields = [
  {
    label: 'Department Name',
    key: 'name',
    fallback: 'Unknown Department'
  },
  {
    label: 'Description',
    key: 'description',
    fallback: 'No description'
  },
  {
    label: 'Head',
    key: 'head',
    fallback: 'No head assigned'
  }
];

/**
 * Court field mappings
 */
export const courtFields = [
  {
    label: 'Court Name',
    key: 'name',
    fallback: 'Unknown Court'
  },
  {
    label: 'Type',
    key: 'type',
    fallback: 'General Court'
  },
  {
    label: 'Location',
    key: 'location',
    fallback: 'No location'
  },
  {
    label: 'Judge',
    key: 'judge',
    fallback: 'No judge assigned'
  }
];

/**
 * Comment field mappings
 */
export const commentFields = [
  {
    label: 'User',
    key: 'userName',
    fallback: 'Anonymous'
  },
  {
    label: 'Role',
    key: 'userRole',
    fallback: 'Unknown Role'
  },
  {
    label: 'Comment',
    key: 'text',
    fallback: 'No comment'
  },
  {
    label: 'Time',
    key: 'timestamp',
    formatter: (value) => value ? new Date(value).toLocaleString() : 'Unknown time',
    fallback: 'Unknown time'
  }
];

/**
 * Helper function to get nested object value using dot notation
 * @param {Object} obj - The object to search in
 * @param {string} path - The dot notation path (e.g., 'user.profile.name')
 * @returns {any} - The value at the specified path
 */
export const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

/**
 * Generic function to generate field array from any configuration
 * @param {Object} data - The data object
 * @param {Array} fieldConfig - The field configuration array
 * @returns {Array} - Array of formatted field objects
 */
export const generateFieldArray = (data, fieldConfig) => {
  if (!data || !fieldConfig) return [];
  
  return fieldConfig.map(field => {
    let value = getNestedValue(data, field.key);
    
    // Apply formatter if provided
    if (field.formatter && typeof field.formatter === 'function') {
      value = field.formatter(value);
    } else {
      // Use fallback if value is null, undefined, or empty string
      value = value || field.fallback;
    }
    
    return {
      label: field.label,
      value: value
    };
  });
};

/**
 * Specific helper functions for each data type
 */
export const generateEvidenceFields = (evidence) => generateFieldArray(evidence, evidenceFields);
export const generateUserFields = (user) => generateFieldArray(user, userFields);
export const generateLabFields = (lab) => generateFieldArray(lab, labFields);
export const generateDepartmentFields = (department) => generateFieldArray(department, departmentFields);
export const generateCourtFields = (court) => generateFieldArray(court, courtFields);
export const generateCommentFields = (comment) => generateFieldArray(comment, commentFields);

/**
 * Custom field configurations for specific use cases
 */
export const customConfigs = {
  // Minimal evidence info for cards
  evidenceCard: [
    {
      label: 'Type',
      key: 'type',
      fallback: 'Unknown'
    },
    {
      label: 'Lab',
      key: 'lab_department',
      formatter: (labDeptArray) => {
        if (!labDeptArray || !Array.isArray(labDeptArray) || labDeptArray.length === 0) {
          return 'Not assigned';
        }
        const labNames = [...new Set(labDeptArray.map(item => item.lab?.name || 'Unknown Lab'))];
        return labNames.join(', ');
      },
      fallback: 'Not assigned'
    }
  ],
  
  // Basic case info for headers
  caseHeader: [
    {
      label: 'FIR',
      key: 'firNumber',
      fallback: 'N/A'
    },
    {
      label: 'Type',
      key: 'caseType',
      fallback: 'N/A'
    }
  ]
};

/**
 * Generate custom field arrays
 */
export const generateEvidenceCardFields = (evidence) => generateFieldArray(evidence, customConfigs.evidenceCard);
export const generateCaseHeaderFields = (caseData) => generateFieldArray(caseData, customConfigs.caseHeader);
