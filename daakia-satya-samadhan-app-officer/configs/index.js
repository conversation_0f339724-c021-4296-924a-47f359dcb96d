/**
 * Configuration exports
 * Central export file for all configuration modules
 */

// Case details configuration
export {
  caseDetailsFields,
  basicCaseDetailsFields,
  extendedCaseDetailsFields,
  generateCaseDetailsArray,
  generateBasicCaseDetailsArray,
  getNestedValue
} from './caseDetailsConfig';

// Field mappings configuration
export {
  evidenceFields,
  userFields,
  labFields,
  departmentFields,
  courtFields,
  commentFields,
  customConfigs,
  generateFieldArray,
  generateEvidenceFields,
  generateUserFields,
  generateLabFields,
  generateDepartmentFields,
  generateCourtFields,
  generateCommentFields,
  generateEvidenceCardFields,
  generateCaseHeaderFields
} from './fieldMappings';

/**
 * Common configuration constants
 */
export const APP_CONFIGS = {
  // Default fallback values
  DEFAULT_FALLBACK: 'N/A',
  DEFAULT_DATE_FORMAT: 'toLocaleString',
  
  // Field display settings
  MAX_FIELD_LENGTH: 100,
  TRUNCATE_SUFFIX: '...',
  
  // Common formatters
  formatters: {
    date: (value) => value ? new Date(value).toLocaleString() : 'N/A',
    shortDate: (value) => value ? new Date(value).toLocaleDateString() : 'N/A',
    time: (value) => value ? new Date(value).toLocaleTimeString() : 'N/A',
    truncate: (value, maxLength = 100) => {
      if (!value) return 'N/A';
      return value.length > maxLength ? value.substring(0, maxLength) + '...' : value;
    },
    capitalize: (value) => {
      if (!value) return 'N/A';
      return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
    },
    arrayToString: (array, separator = ', ') => {
      if (!Array.isArray(array) || array.length === 0) return 'None';
      return array.join(separator);
    }
  }
};

/**
 * Validation helpers
 */
export const validators = {
  isValidField: (field) => {
    return field && typeof field === 'object' && field.label && field.key;
  },
  
  isValidFieldArray: (fields) => {
    return Array.isArray(fields) && fields.every(validators.isValidField);
  },
  
  hasValue: (value) => {
    return value !== null && value !== undefined && value !== '';
  }
};

/**
 * Configuration builder helpers
 */
export const configHelpers = {
  /**
   * Create a custom field configuration
   * @param {string} label - Display label
   * @param {string} key - Object property key
   * @param {Function} formatter - Optional formatter function
   * @param {string} fallback - Fallback value
   * @returns {Object} Field configuration object
   */
  createField: (label, key, formatter = null, fallback = 'N/A') => ({
    label,
    key,
    ...(formatter && { formatter }),
    fallback
  }),
  
  /**
   * Merge multiple field configurations
   * @param {...Array} fieldArrays - Field configuration arrays to merge
   * @returns {Array} Merged field configuration array
   */
  mergeFields: (...fieldArrays) => {
    return fieldArrays.flat().filter(validators.isValidField);
  },
  
  /**
   * Filter fields by keys
   * @param {Array} fields - Field configuration array
   * @param {Array} keys - Keys to include
   * @returns {Array} Filtered field configuration array
   */
  filterFields: (fields, keys) => {
    return fields.filter(field => keys.includes(field.key));
  },
  
  /**
   * Exclude fields by keys
   * @param {Array} fields - Field configuration array
   * @param {Array} keys - Keys to exclude
   * @returns {Array} Filtered field configuration array
   */
  excludeFields: (fields, keys) => {
    return fields.filter(field => !keys.includes(field.key));
  }
};
