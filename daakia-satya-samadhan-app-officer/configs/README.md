# Configuration Files Documentation

This directory contains reusable configuration files for field mappings and data display across the application.

## Files Overview

### 1. `caseDetailsConfig.js`
Contains configuration for case details field mappings.

**Exports:**
- `caseDetailsFields` - Full case details field configuration
- `basicCaseDetailsFields` - Basic case details for simple views
- `extendedCaseDetailsFields` - Extended case details with additional fields
- `generateCaseDetailsArray(caseDetails)` - Generate formatted case details array
- `generateBasicCaseDetailsArray(caseDetails)` - Generate basic case details array

### 2. `fieldMappings.js`
Contains configuration for various data types (evidence, users, labs, etc.).

**Exports:**
- Field configurations: `evidenceFields`, `userFields`, `labFields`, `departmentFields`, `courtFields`, `commentFields`
- Generator functions: `generateEvidenceFields()`, `generateUserFields()`, etc.
- Custom configurations: `customConfigs.evidenceCard`, `customConfigs.caseHeader`
- Generic helper: `generateFieldArray(data, fieldConfig)`

### 3. `index.js`
Central export file with additional utilities.

**Exports:**
- All exports from other config files
- `APP_CONFIGS` - Common configuration constants and formatters
- `validators` - Validation helper functions
- `configHelpers` - Configuration builder utilities

## Usage Examples

### Basic Usage in Components

```javascript
import { generateCaseDetailsArray } from '../../../../configs';

// In your component
const caseDetailsArray = generateCaseDetailsArray(caseDetails);

// Render the fields
{caseDetailsArray.map((detail, index) => (
  <View key={index}>
    <Text>{detail.label}</Text>
    <Text>{detail.value}</Text>
  </View>
))}
```

### Using Different Field Sets

```javascript
import { 
  generateBasicCaseDetailsArray,
  generateEvidenceFields,
  generateUserFields 
} from '../../../../configs';

// For different views
const basicDetails = generateBasicCaseDetailsArray(caseDetails);
const evidenceInfo = generateEvidenceFields(evidence);
const userInfo = generateUserFields(user);
```

### Custom Field Configuration

```javascript
import { generateFieldArray, configHelpers } from '../../../../configs';

// Create custom field configuration
const customFields = [
  configHelpers.createField('Custom Label', 'customKey', null, 'Default Value'),
  configHelpers.createField('Formatted Date', 'dateField', 
    (value) => value ? new Date(value).toLocaleDateString() : 'No date'
  )
];

// Use with any data
const customData = generateFieldArray(myData, customFields);
```

### Using Formatters

```javascript
import { APP_CONFIGS } from '../../../../configs';

// Use built-in formatters
const formattedDate = APP_CONFIGS.formatters.date(dateValue);
const truncatedText = APP_CONFIGS.formatters.truncate(longText, 50);
const capitalizedText = APP_CONFIGS.formatters.capitalize(text);
```

### Merging and Filtering Fields

```javascript
import { configHelpers, caseDetailsFields, userFields } from '../../../../configs';

// Merge multiple field configurations
const combinedFields = configHelpers.mergeFields(caseDetailsFields, userFields);

// Filter specific fields
const specificFields = configHelpers.filterFields(caseDetailsFields, ['title', 'firNumber']);

// Exclude certain fields
const filteredFields = configHelpers.excludeFields(caseDetailsFields, ['remarks']);
```

## Benefits

1. **Maintainability**: All field mappings are centralized and easy to update
2. **Reusability**: Same configurations can be used across multiple components
3. **Consistency**: Ensures consistent data display throughout the app
4. **Flexibility**: Easy to create custom field sets for different use cases
5. **Type Safety**: Structured approach reduces errors in field mapping

## Adding New Configurations

To add new field configurations:

1. Add the field array to the appropriate config file
2. Create a generator function if needed
3. Export from the index.js file
4. Update this README with usage examples

Example:
```javascript
// In fieldMappings.js
export const newDataFields = [
  {
    label: 'Field Label',
    key: 'fieldKey',
    formatter: (value) => /* custom formatting */,
    fallback: 'Default Value'
  }
];

export const generateNewDataFields = (data) => generateFieldArray(data, newDataFields);
```

## Field Configuration Structure

Each field configuration object should have:
- `label` (string): Display label for the field
- `key` (string): Property key in the data object (supports dot notation)
- `formatter` (function, optional): Function to format the value
- `fallback` (string, optional): Fallback value when data is missing (default: 'N/A')

## Best Practices

1. Use descriptive labels that match your UI requirements
2. Keep fallback values consistent across similar fields
3. Use formatters for complex data transformations
4. Group related fields in logical configurations
5. Test field configurations with various data scenarios
6. Document any custom formatters or complex logic
