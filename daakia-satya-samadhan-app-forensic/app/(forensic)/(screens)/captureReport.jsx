import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import { useLocalSearchParams, useRouter } from "expo-router";
import { useAuth } from '../../../context/auth-context';
import * as DocumentPicker from 'expo-document-picker';
import { Ionicons } from '@expo/vector-icons';
import MediaDetailsModal from '../../../components/Larges/MediaDetailsModal';
import DocumentCapture from '../../../components/DocumentCapture';
import Constants from 'expo-constants';
import useUploadMedia from '../../../hooks/useUploadMedia';

import { Colors } from '../../../constants/colors';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

const CaptureReport = () => {
  const router = useRouter();
  const { evidenceId, caseId, forensicRequestId } = useLocalSearchParams();
  const { token } = useAuth();
  const { uploadMedia, isUploading: isUploadingMedia } = useUploadMedia();
  
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // Handle photo capture
  const handleOpenCamera = () => {
    setCameraVisible(true);
  };

  // Handle PDF document selection
  const handleSelectPDF = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
      });
  
      // Check if the selection was not canceled and there are assets
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        const fileName = file.name || 'document.pdf';
        addMediaItem(file.uri, 'pdf', fileName);
      }
    } catch (error) {
      console.error('Error picking PDF document:', error);
      Alert.alert('Error', 'Failed to pick PDF document');
    }
  };

  // Helper function to add media items to the state
  const addMediaItem = (uri, type, name = '') => {
    const mediaId = Date.now().toString();
    setMedia(prevMedia => [...prevMedia, { id: mediaId, uri, type, name }]);
  };

  // Handle photo captured from camera
  const handlePhotoCaptured = (uri) => {
    addMediaItem(uri, 'image');
    setCameraVisible(false);
  };

  // Handle media deletion
  const handleDeleteMedia = (index) => {
    setMedia(prevMedia => prevMedia.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = () => {
    if (media.length === 0) {
      Alert.alert('Error', 'Please add at least one photo or PDF document');
      return;
    }
    
    setModalVisible(true);
  };

  // Upload all media files and return array of URLs
  const uploadAllMedia = async () => {
    const uploadPromises = media.map(async (item) => {
      const fileUrl = await uploadMedia(item.uri, item.type);
      if (!fileUrl) {
        throw new Error(`Failed to upload ${item.type}`);
      }
      return fileUrl;
    });

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  };


  const submitReportToAPI = async (title, description, fileUrls) => {
    try {
      const mediaType = media[0].type;
      let type = mediaType;
      
      // If it's an image, add the extension
      if (mediaType === 'image') {
        type = 'image.jpg';
      }

      const payload = {
        forensicRequestId,
        type,
        title,
        description,
        attachmentUrl: fileUrls
      };

      const response = await fetch(`${BASE_URL}/api/forensic/report/case/${caseId}/evidence/${evidenceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      console.log('API Response:', result);
      if (response.ok) {
        return result;
      } else {
        throw new Error(result.message || 'Failed to submit report');
      }
    } catch (error) {
      console.error('API submission error:', error);
      throw error;
    }
  };

  const handleModalSubmit = async ({ title, description }) => {
    // Check if title or description is empty
    if (!title || !title.trim()) {
      Alert.alert('Validation Error', 'Please enter a title for the report');
      return;
    }
    
    if (!description || !description.trim()) {
      Alert.alert('Validation Error', 'Please enter a description for the report');
      return;
    }
    
    try {
      setIsUploading(true);
      const fileUrls = await uploadAllMedia();
      const result = await submitReportToAPI(title, description, fileUrls);
      const reportData = {
        attachmentUrl: JSON.stringify(result.data.attachmentUrl),
        description: result.data.description,
        title: result.data.title,
        reportId: result.data._id 
      };
      
      router.replace({
        pathname: '(screens)/viewAllDocumnets',
        params: { ...reportData, forensicRequestId } 
      });
      
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to submit report');
    } finally {
      setIsUploading(false);
      setModalVisible(false);
    }
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <ScrollView 
          style={styles.content} 
          contentContainerStyle={styles.scrollContent}
        >
          <CapturePageHeader
            title="Upload Case Report *"
            subtitle="Please upload the case report ensuring it includes all files and the official seal."
          />

          <View style={styles.actionButtonContainer}>
            <TouchableOpacity 
              style={styles.captureButton} 
              onPress={handleOpenCamera}
              disabled={isUploading}
            >
              <Ionicons name="camera" size={24} color="white" />
              <Text style={styles.buttonText}>Take Photo</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.pdfButton} 
              onPress={handleSelectPDF}
              disabled={isUploading}
            >
              <Ionicons name="document-text" size={24} color="white" />
              <Text style={styles.buttonText}>Select PDF</Text>
            </TouchableOpacity>
          </View>

          {media.length > 0 && (
            <View style={styles.mediaContainer}>
              <Text style={styles.mediaTitle}>
                Selected Files ({media.length})
              </Text>
              <MediaGrid
                media={media}
                onDeleteMedia={handleDeleteMedia}
                thumbnailSize={150}
              />
            </View>
          )}
        </ScrollView>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (media.length === 0 || isUploading) && styles.disabledButton
            ]}
            onPress={handleSubmit}
            disabled={media.length === 0 || isUploading}
          >
            <Text style={styles.submitButtonText}>
              {isUploading ? 'Uploading...' : 'Upload'}
            </Text>
          </TouchableOpacity>
        </View>

        {cameraVisible && (
          <View style={styles.cameraOverlay}>
            <DocumentCapture
              onComplete={(documents) => {
                documents.forEach(doc => {
                  addMediaItem(doc.uri, doc.type, doc.name);
                });
                setCameraVisible(false);
              }}
              backPressed={() => setCameraVisible(false)}
            />
          </View>
        )}

        <MediaDetailsModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onSubmit={handleModalSubmit}
          media={media}
          isLoading={isUploading}
        />
      </View>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  captureButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  pdfButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  buttonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
    fontFamily: 'Roboto',
  },
  mediaContainer: {
    marginTop: 15,
  },
  mediaTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 15,
    marginBottom: 5,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',

  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    fontFamily: 'Roboto',
    fontWeight:'bold',
  },
  submitButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight:'bold',
    fontFamily: 'Roboto',
  },
});

export default CaptureReport;