import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image, ScrollView, Platform, PermissionsAndroid, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DocumentScanner from 'react-native-document-scanner-plugin';
import { Colors } from '../constants/colors';
import * as Print from 'expo-print';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';

const DocumentCapture = ({ onComplete }) => {
  const [showScanner, setShowScanner] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [isCreatingPDF, setIsCreatingPDF] = useState(false);
  const [pdfUri, setPdfUri] = useState(null);

  const handleScanDocument = async () => {
    try {
      // Request camera permissions on Android
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: "Camera Permission",
            message: "App needs camera permission to scan documents",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK"
          }
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Permission Denied', 'Camera permission is required to scan documents');
          return;
        }
      }

      // Start the document scanner
      const result = await DocumentScanner.scanDocument();
      
      console.warn('Scanner Result:', result);
      
      // Check if result exists and has scannedImages property
      if (result && result.scannedImages && result.scannedImages.length > 0) {
        console.log('Scanned Image URIs:', result.scannedImages);
        
        // Add the scanned images to documents array
        const newDocuments = result.scannedImages.map(uri => {
          console.log('Processing URI:', uri);
          return {
            uri,
            type: 'image',
            name: `scan_${Date.now()}.jpg`
          };
        });
        
        setDocuments(prev => [...prev, ...newDocuments]);
      }
    } catch (error) {
      console.error('Error scanning document:', error);
      Alert.alert('Error', 'Failed to scan document. Please try again.');
    }
  };

  const removeDocument = (index) => {
    const newDocuments = [...documents];
    newDocuments.splice(index, 1);
    setDocuments(newDocuments);
  };

  const createPDF = async () => {
    if (documents.length === 0) {
      Alert.alert('Error', 'No documents to create PDF from');
      return;
    }

    try {
      setIsCreatingPDF(true);
      
      // Convert images to base64 first
      const base64Images = await Promise.all(
        documents.map(async (doc) => {
          try {
            const base64 = await FileSystem.readAsStringAsync(doc.uri, {
              encoding: FileSystem.EncodingType.Base64,
            });
            return `data:image/jpeg;base64,${base64}`;
          } catch (error) {
            console.error('Error converting image to base64:', error);
            throw error;
          }
        })
      );

      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }
              body { 
                margin: 0; 
                padding: 0; 
                background-color: white;
              }
              .page { 
                width: 100vw;
                height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
                page-break-after: always;
                margin: 0;
                padding: 0;
                overflow: hidden;
              }
              .image-container {
                width: 100%;
                height: 100%;
                position: relative;
              }
              img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: fill;
                display: block;
              }
            </style>
          </head>
          <body>
            ${base64Images.map((base64) => `
              <div class="page">
                <div class="image-container">
                  <img src="${base64}" />
                </div>
              </div>
            `).join('')}
          </body>
        </html>
      `;

      // Generate PDF with zero margins
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        width: 595.28,  // A4 width in points
        height: 841.89, // A4 height in points
        base64: false,
        margins: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
        }
      });

      setPdfUri(uri);
      Alert.alert('Success', 'PDF created successfully!');

    } catch (error) {
      console.error('Error creating PDF:', error);
      Alert.alert('Error', 'Failed to create PDF. Please try again.');
    } finally {
      setIsCreatingPDF(false);
    }
  };

  const previewPDF = async () => {
    if (!pdfUri) {
      Alert.alert('Error', 'No PDF available to preview');
      return;
    }

    try {
      if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(pdfUri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      } else {
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(pdfUri);
        } else {
          Alert.alert('Error', 'PDF preview is not available on this device');
        }
      }
    } catch (error) {
      console.error('Error previewing PDF:', error);
      Alert.alert('Error', 'Failed to preview PDF');
    }
  };

  const handleComplete = async () => {
    if (!pdfUri) {
      Alert.alert('Error', 'Please create PDF first');
      return;
    }

    try {
      // Only send the PDF back to parent component
      onComplete([{
        uri: pdfUri,
        type: 'pdf',
        name: `document_${Date.now()}.pdf`
      }]);
    } catch (error) {
      console.error('Error completing:', error);
      Alert.alert('Error', 'Failed to complete. Please try again.');
    }
  };

  const cleanup = async () => {
    if (pdfUri) {
      try {
        await FileSystem.deleteAsync(pdfUri);
      } catch (error) {
        console.error('Error cleaning up PDF:', error);
      }
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Document Scanner</Text>
      
      {documents.length === 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="document-text-outline" size={80} color="#ccc" />
          <Text style={styles.emptyText}>No documents scanned yet</Text>
          <TouchableOpacity 
            style={styles.scanButton}
            onPress={handleScanDocument}
          >
            <Ionicons name="scan-outline" size={24} color="white" />
            <Text style={styles.buttonText}>Scan Document</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <ScrollView style={styles.documentList}>
            {documents.map((doc, index) => (
              <View key={index} style={styles.documentItem}>
                <Image
                  source={{ uri: doc.uri }}
                  style={styles.documentPreview}
                  resizeMode="cover"
                />
                {!pdfUri && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeDocument(index)}
                  >
                    <Ionicons name="trash-outline" size={24} color="white" />
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </ScrollView>

          <View style={styles.footer}>
            {!pdfUri ? (
              <>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleScanDocument}
                >
                  <Ionicons name="add" size={24} color={Colors.primary} />
                  <Text style={styles.addButtonText}>Add More</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.pdfButton, isCreatingPDF && styles.disabledButton]}
                  onPress={createPDF}
                  disabled={isCreatingPDF}
                >
                  <Ionicons name="document-text" size={24} color="white" />
                  <Text style={styles.buttonText}>
                    {isCreatingPDF ? 'Creating PDF...' : 'Create PDF'}
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.pdfButton}
                  onPress={previewPDF}
                >
                  <Ionicons name="eye" size={24} color="white" />
                  <Text style={styles.buttonText}>Preview PDF</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.completeButton}
                  onPress={handleComplete}
                >
                  <Text style={styles.completeButtonText}>Complete</Text>
                  <Ionicons name="checkmark-circle" size={24} color="white" />
                </TouchableOpacity>
              </>
            )}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginVertical: 20,
  },
  scanButton: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  documentList: {
    flex: 1,
  },
  documentItem: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  documentPreview: {
    width: '100%',
    height: 200,
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#fa5252',
    padding: 8,
    borderRadius: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  addButtonText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  completeButton: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  completeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  pdfButton: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
});

export default DocumentCapture; 